<?php
/**
 * 微信配置测试文件
 * 用于验证微信配置是否正确
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Config;
use WeMini\Crypt;
use WeChat\Oauth;

// 模拟配置加载
$wechatConfig = [
    // 微信小程序配置
    'miniapp' => [
        'appid' => 'your_miniapp_appid',
        'appsecret' => 'your_miniapp_secret',
        'token' => 'your_miniapp_token',
        'aes_key' => 'your_miniapp_aes_key',
    ],
    
    // 微信公众号配置
    'official' => [
        'appid' => 'your_official_appid',
        'appsecret' => 'your_official_secret',
        'token' => 'your_official_token',
        'aes_key' => 'your_official_aes_key',
    ],
];

echo "=== 微信配置测试 ===\n\n";

// 测试小程序配置
echo "1. 测试微信小程序配置:\n";
try {
    $miniConfig = $wechatConfig['miniapp'];
    $miniCrypt = Crypt::instance($miniConfig);
    echo "✓ 微信小程序 Crypt 类实例化成功\n";
    echo "  AppID: " . $miniConfig['appid'] . "\n";
    echo "  配置完整性: " . (empty($miniConfig['appid']) || empty($miniConfig['appsecret']) ? "❌ 缺少必要配置" : "✓ 配置完整") . "\n";
} catch (Exception $e) {
    echo "❌ 微信小程序配置错误: " . $e->getMessage() . "\n";
}

echo "\n";

// 测试公众号配置
echo "2. 测试微信公众号配置:\n";
try {
    $officialConfig = $wechatConfig['official'];
    $oauth = Oauth::instance($officialConfig);
    echo "✓ 微信公众号 Oauth 类实例化成功\n";
    echo "  AppID: " . $officialConfig['appid'] . "\n";
    echo "  配置完整性: " . (empty($officialConfig['appid']) || empty($officialConfig['appsecret']) ? "❌ 缺少必要配置" : "✓ 配置完整") . "\n";
    
    // 测试生成授权URL
    $testRedirectUri = 'https://your-domain.com/callback';
    $authUrl = $oauth->getOauthRedirect($testRedirectUri, 'test_state', 'snsapi_userinfo');
    echo "  授权URL生成: " . (empty($authUrl) ? "❌ 失败" : "✓ 成功") . "\n";
    if (!empty($authUrl)) {
        echo "  示例URL: " . substr($authUrl, 0, 100) . "...\n";
    }
} catch (Exception $e) {
    echo "❌ 微信公众号配置错误: " . $e->getMessage() . "\n";
}

echo "\n";

// 测试类是否正确加载
echo "3. 测试依赖类加载:\n";
echo "  WeMini\\Crypt: " . (class_exists('WeMini\\Crypt') ? "✓ 已加载" : "❌ 未加载") . "\n";
echo "  WeChat\\Oauth: " . (class_exists('WeChat\\Oauth') ? "✓ 已加载" : "❌ 未加载") . "\n";
echo "  zoujingli/wechat-developer: " . (class_exists('We') ? "✓ 已加载" : "❌ 未加载") . "\n";

echo "\n";

// 配置建议
echo "4. 配置建议:\n";
echo "  - 请确保在 .env 文件中正确配置微信相关参数\n";
echo "  - 微信小程序需要配置: WECHAT_MINIAPP_APPID, WECHAT_MINIAPP_SECRET\n";
echo "  - 微信公众号需要配置: WECHAT_OFFICIAL_APPID, WECHAT_OFFICIAL_SECRET\n";
echo "  - 确保微信后台配置了正确的服务器域名和回调地址\n";
echo "  - 生产环境建议配置微信开放平台以获取 UnionID\n";

echo "\n=== 测试完成 ===\n";
