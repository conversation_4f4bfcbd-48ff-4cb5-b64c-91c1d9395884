<?php
use think\facade\Route;
use app\common_api\middleware\ApiCheck;

Route::group('commonUser', function () {
    Route::post('getTokenByPwd', 'app\common_api\controller\CommonUser@getTokenByPwd');
    Route::post('emailRegister', 'app\common_api\controller\CommonUser@emailRegister');
    Route::post('sendEmailCode', 'app\common_api\controller\CommonUser@sendEmailCode');
    Route::post('resetPassword', 'app\common_api\controller\CommonUser@resetPassword');
    Route::get('testUser', 'app\common_api\controller\CommonUser@testUser');
    Route::post('testEmailRegister', 'app\common_api\controller\TestEventController@testEmailRegister');
});

Route::group('commonUser', function () {
    Route::post('getUserInfo', 'app\common_api\controller\CommonUser@getUserInfo');
    Route::post('updateNickname', 'app\common_api\controller\CommonUser@updateNickname');
    Route::post('uploadAvatar', 'app\common_api\controller\CommonUser@uploadAvatar');
    Route::post('changePassword', 'app\common_api\controller\CommonUser@changePassword');
})->middleware([ApiCheck::class]);


Route::group('weixin', function () {
    Route::post('miniappLogin', 'app\common_api\controller\Weixin@miniappLogin');
    Route::post('updateNickname', 'app\common_api\controller\Weixin@updateNickname');
    Route::post('uploadAvatar', 'app\common_api\controller\Weixin@uploadAvatar');
    Route::post('changePassword', 'app\common_api\controller\Weixin@changePassword');
})->middleware([ApiCheck::class]);

// 通用上传接口
Route::group('common', function () {
    // 单张图片上传
    Route::post('uploadImage', 'app\common_api\controller\CommonUpload@uploadImage');

    // 批量图片上传
    Route::post('uploadImages', 'app\common_api\controller\CommonUpload@uploadImages');

    // 删除图片
    Route::post('deleteImage', 'app\common_api\controller\CommonUpload@deleteImage');

    // 获取用户图片列表
    Route::post('getUserImages', 'app\common_api\controller\CommonUpload@getUserImages');
})->middleware([ApiCheck::class]);


