# 应用配置
APP_DEBUG = true

# 数据库配置
DATABASE_HOSTNAME = 127.0.0.1
DATABASE_DATABASE = your_database
DATABASE_USERNAME = your_username
DATABASE_PASSWORD = your_password
DATABASE_HOSTPORT = 3306

# 微信小程序配置
WECHAT_MINIAPP_APPID = your_miniapp_appid
WECHAT_MINIAPP_SECRET = your_miniapp_secret
WECHAT_MINIAPP_TOKEN = your_miniapp_token
WECHAT_MINIAPP_AES_KEY = your_miniapp_aes_key

# 微信公众号配置
WECHAT_OFFICIAL_APPID = your_official_appid
WECHAT_OFFICIAL_SECRET = your_official_secret
WECHAT_OFFICIAL_TOKEN = your_official_token
WECHAT_OFFICIAL_AES_KEY = your_official_aes_key

# 微信开放平台配置
WECHAT_OPEN_APPID = your_open_appid
WECHAT_OPEN_SECRET = your_open_secret

# JWT配置
JWT_SECRET = your_jwt_secret_key

# 邮件配置
MAIL_HOST = smtp.example.com
MAIL_PORT = 587
MAIL_USERNAME = <EMAIL>
MAIL_PASSWORD = your_email_password
MAIL_FROM_ADDRESS = <EMAIL>
MAIL_FROM_NAME = "Your App Name"

# 云存储配置
CLOUD_STORAGE_TYPE = qiniu
QINIU_ACCESS_KEY = your_qiniu_access_key
QINIU_SECRET_KEY = your_qiniu_secret_key
QINIU_BUCKET = your_qiniu_bucket
QINIU_DOMAIN = your_qiniu_domain
