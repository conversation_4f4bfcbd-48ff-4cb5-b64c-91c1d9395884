<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/app" isTestSource="false" packagePrefix="app\" />
      <sourceFolder url="file://$MODULE_DIR$/extend" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lcobucci/clock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lcobucci/jwt" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/flysystem" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/flysystem-cached-adapter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/mime-type-detection" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/thans/tp-jwt-auth" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/framework" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-filesystem" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-helper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-multi-app" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-orm" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-trace" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-validate" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/aliyuncs/oss-sdk-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/command" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/guzzle-services" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/myclabs/php-enum" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/qcloud/cos-sdk-v5" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/qiniu/php-sdk" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpmailer/phpmailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/yansongda/artful" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/yansongda/supports" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/zoujingli/wechat-developer" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>