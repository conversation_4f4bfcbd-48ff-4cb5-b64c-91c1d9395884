# 微信登录功能测试示例

## 前端测试代码示例

### 1. 微信小程序登录测试

```javascript
// 小程序端代码
// pages/login/login.js
Page({
  data: {
    userInfo: null
  },

  // 微信小程序登录
  wechatLogin() {
    const that = this;
    
    // 获取登录凭证
    wx.login({
      success(res) {
        if (res.code) {
          console.log('获取code成功：', res.code);
          
          // 获取用户信息（可选）
          wx.getUserProfile({
            desc: '用于完善用户资料',
            success(userRes) {
              console.log('获取用户信息成功：', userRes);
              
              // 调用后端登录接口
              that.callLoginApi(res.code, userRes.encryptedData, userRes.iv);
            },
            fail() {
              // 即使用户拒绝授权，也可以只用code登录
              that.callLoginApi(res.code);
            }
          });
        } else {
          console.log('登录失败！' + res.errMsg);
        }
      }
    });
  },

  // 调用后端登录API
  callLoginApi(code, encryptedData = '', iv = '') {
    wx.request({
      url: 'https://your-domain.com/api/wechat/miniapp/login',
      method: 'POST',
      data: {
        code: code,
        encryptedData: encryptedData,
        iv: iv
      },
      success(res) {
        console.log('登录成功：', res.data);
        
        if (res.data.code === 1) {
          // 保存token
          wx.setStorageSync('token', res.data.data.token);
          wx.setStorageSync('userInfo', res.data.data.user_info);
          
          // 跳转到主页
          wx.switchTab({
            url: '/pages/index/index'
          });
        } else {
          wx.showToast({
            title: res.data.msg,
            icon: 'none'
          });
        }
      },
      fail(err) {
        console.error('登录请求失败：', err);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    });
  }
});
```

### 2. 微信公众号登录测试

```html
<!-- 公众号H5页面 -->
<!DOCTYPE html>
<html>
<head>
    <title>微信公众号登录</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <div id="app">
        <button onclick="wechatLogin()">微信登录</button>
        <div id="result"></div>
    </div>

    <script>
        // 获取微信授权URL并跳转
        function wechatLogin() {
            const redirectUri = encodeURIComponent(window.location.origin + '/wechat-callback.html');
            
            fetch('/api/wechat/official/auth-url?redirect_uri=' + redirectUri)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        // 跳转到微信授权页面
                        window.location.href = data.data.auth_url;
                    } else {
                        alert('获取授权URL失败：' + data.msg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('请求失败');
                });
        }

        // 处理微信授权回调
        function handleWechatCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            
            if (code) {
                fetch('/api/wechat/official/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ code: code })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        // 保存token
                        localStorage.setItem('token', data.data.token);
                        localStorage.setItem('userInfo', JSON.stringify(data.data.user_info));
                        
                        document.getElementById('result').innerHTML = 
                            '<p>登录成功！</p>' +
                            '<p>用户昵称：' + data.data.user_info.nickname + '</p>' +
                            '<p>Token：' + data.data.token + '</p>';
                    } else {
                        alert('登录失败：' + data.msg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('登录请求失败');
                });
            }
        }

        // 如果URL中有code参数，说明是从微信授权页面回调
        if (window.location.search.includes('code=')) {
            handleWechatCallback();
        }
    </script>
</body>
</html>
```

## 后端测试

### 1. 使用Postman测试

#### 微信小程序登录接口测试
```
POST /api/wechat/miniapp/login
Content-Type: application/json

{
    "code": "081234567890abcdef"
}
```

#### 微信公众号登录接口测试
```
POST /api/wechat/official/login
Content-Type: application/json

{
    "code": "081234567890abcdef"
}
```

#### 获取公众号授权URL测试
```
GET /api/wechat/official/auth-url?redirect_uri=https://your-domain.com/callback
```

### 2. PHP测试脚本

```php
<?php
// test_wechat_login.php

// 测试微信小程序登录
function testMiniappLogin() {
    $url = 'http://your-domain.com/api/wechat/miniapp/login';
    $data = [
        'code' => 'test_code_123456'  // 这里需要真实的微信code
    ];
    
    $options = [
        'http' => [
            'header' => "Content-type: application/json\r\n",
            'method' => 'POST',
            'content' => json_encode($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    echo "微信小程序登录测试结果：\n";
    echo $result . "\n\n";
}

// 测试获取公众号授权URL
function testGetAuthUrl() {
    $url = 'http://your-domain.com/api/wechat/official/auth-url?redirect_uri=' . urlencode('https://your-domain.com/callback');
    
    $result = file_get_contents($url);
    
    echo "获取公众号授权URL测试结果：\n";
    echo $result . "\n\n";
}

// 运行测试
testMiniappLogin();
testGetAuthUrl();
?>
```

## 配置检查清单

### 1. 环境配置检查
- [ ] `.env` 文件中微信配置参数是否正确填写
- [ ] 微信小程序/公众号后台配置是否正确
- [ ] 服务器域名是否已在微信后台配置

### 2. 数据库检查
- [ ] `common_user` 表是否存在
- [ ] 表结构是否包含微信相关字段（union_id, openid_mp, openid_qq等）

### 3. 依赖检查
- [ ] `zoujingli/wechat-developer` 包是否正确安装
- [ ] JWT相关配置是否正确

### 4. 权限检查
- [ ] 路由是否正确配置
- [ ] 中间件是否正常工作

## 常见问题排查

### 1. 获取不到openid
- 检查微信配置参数是否正确
- 检查code是否有效（code只能使用一次）
- 检查网络连接是否正常

### 2. 用户信息解密失败
- 检查session_key是否正确
- 检查encryptedData和iv参数是否完整
- 确认微信小程序版本支持getUserProfile

### 3. Token验证失败
- 检查JWT配置是否正确
- 确认token格式是否正确
- 检查token是否过期

### 4. 数据库操作失败
- 检查数据库连接是否正常
- 确认表结构是否正确
- 检查字段长度是否足够
