<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

Route::get('think', function () {
    return 'hello,ThinkPHP8!';
});

Route::get('hello/:name', 'index/hello');


// 微信登录相关路由组
Route::group('api/wechat', function () {
    // 微信小程序登录
    Route::post('miniapp/login', 'common_api.Weixin/miniappLogin');

    // 微信公众号登录
    Route::post('official/login', 'common_api.Weixin/officialLogin');

    // 获取微信公众号授权URL
    Route::get('official/auth-url', 'common_api.Weixin/getOfficialAuthUrl');

    // 微信小程序手机号授权登录
    Route::post('miniapp/phone-login', 'common_api.Weixin/miniappPhoneLogin');

    // 绑定微信账号到现有用户（需要登录）
    Route::post('bind', 'common_api.Weixin/bindWechat')->middleware(['app\common_api\middleware\ApiCheck']);
});
