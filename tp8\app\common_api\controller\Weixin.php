<?php

namespace app\common_api\controller;

use app\common_api\service\WeixinService;
use thans\jwt\facade\JWTAuth;

class Weixin extends Base
{
    /**
     * @var WeixinService
     */
    protected $service;

    public function initialize(): void
    {
        $this->service = new WeixinService();
    }

    /**
     * 微信小程序登录
     * @return \think\Response
     */
    public function miniappLogin()
    {
        $params = $this->request->param();

        // 验证必要参数
        if (empty($params['code'])) {
            return fRet('code参数不能为空');
        }

        // 调用微信小程序登录服务
        $result = $this->service->miniappLogin($params);

        if (!$result) {
            return fRet('微信小程序登录失败');
        }

        // 生成JWT token
        $token = JWTAuth::builder(['user_id' => $result['user_id']]);

        return sRet([
            'token' => $token,
            'user_info' => $result['user_info'],
            'openid' => $result['openid']
        ], '登录成功');
    }

    /**
     * 微信公众号登录
     * @return \think\Response
     */
    public function officialLogin()
    {
        $params = $this->request->param();

        // 验证必要参数
        if (empty($params['code'])) {
            return fRet('code参数不能为空');
        }

        // 调用微信公众号登录服务
        $result = $this->service->officialLogin($params);

        if (!$result) {
            return fRet('微信公众号登录失败');
        }

        // 生成JWT token
        $token = JWTAuth::builder(['user_id' => $result['user_id']]);

        return sRet([
            'token' => $token,
            'user_info' => $result['user_info'],
            'openid' => $result['openid']
        ], '登录成功');
    }

    /**
     * 获取微信公众号授权URL
     * @return \think\Response
     */
    public function getOfficialAuthUrl()
    {
        $params = $this->request->param();

        // 验证必要参数
        if (empty($params['redirect_uri'])) {
            return fRet('redirect_uri参数不能为空');
        }

        $redirectUri = $params['redirect_uri'];
        $state = $params['state'] ?? '';

        $authUrl = $this->service->getOfficialAuthUrl($redirectUri, $state);

        if (empty($authUrl)) {
            return fRet('获取授权URL失败');
        }

        return sRet(['auth_url' => $authUrl], '获取授权URL成功');
    }

    /**
     * 微信小程序手机号授权登录
     * @return \think\Response
     */
    public function miniappPhoneLogin()
    {
        $params = $this->request->param();

        // 验证必要参数
        if (empty($params['code']) || empty($params['phone_code'])) {
            return fRet('code和phone_code参数不能为空');
        }

        // 先进行小程序登录获取用户信息
        $loginResult = $this->service->miniappLogin($params);

        if (!$loginResult) {
            return fRet('微信小程序登录失败');
        }

        // TODO: 这里可以添加手机号解密和绑定逻辑
        // 目前先返回基本登录结果

        // 生成JWT token
        $token = JWTAuth::builder(['user_id' => $loginResult['user_id']]);

        return sRet([
            'token' => $token,
            'user_info' => $loginResult['user_info'],
            'openid' => $loginResult['openid']
        ], '登录成功');
    }

    /**
     * 绑定微信账号到现有用户
     * @return \think\Response
     */
    public function bindWechat()
    {
        $params = $this->request->param();
        $userId = request()->user_id; // 从中间件获取当前登录用户ID

        if (empty($userId)) {
            return fRet('请先登录');
        }

        // 验证必要参数
        if (empty($params['code']) || empty($params['platform'])) {
            return fRet('code和platform参数不能为空');
        }

        $platform = $params['platform']; // miniapp 或 official

        // 根据平台调用对应的登录方法获取微信信息
        if ($platform === 'miniapp') {
            $wechatResult = $this->service->miniappLogin($params);
        } elseif ($platform === 'official') {
            $wechatResult = $this->service->officialLogin($params);
        } else {
            return fRet('不支持的平台类型');
        }

        if (!$wechatResult) {
            return fRet('获取微信信息失败');
        }

        // TODO: 这里应该实现绑定逻辑，将微信信息绑定到现有用户
        // 目前先返回成功

        return sRet([], '绑定成功');
    }
}