<?php

namespace app\common_api\service;

use app\common_api\model\CommonUserModel;
use app\common_api\event\UserRegistered;
use app\lib\mylog\MyLog;
use think\facade\Event;
use think\facade\Config;
use WeMini\Crypt;
use WeChat\Oauth;

class WeixinService extends BaseService
{
    protected $commonUserModel;
    
    public function __construct()
    {
        $this->commonUserModel = new CommonUserModel();
    }
    
    /**
     * 微信小程序登录
     * @param array $params
     * @return array|false
     */
    public function miniappLogin($params)
    {
        try {
            $code = $params['code'] ?? '';
            $encryptedData = $params['encryptedData'] ?? '';
            $iv = $params['iv'] ?? '';

            if (empty($code)) {
                return false;
            }

            // 获取微信小程序配置
            $config = Config::get('wechat.miniapp');

            // 通过code获取session_key和openid
            $miniCrypt = Crypt::instance($config);
            $result = $miniCrypt->session($code);

            if (empty($result['openid'])) {
                MyLog::error('微信小程序登录失败：获取openid失败', $result);
                return false;
            }

            $openid = $result['openid'];
            $sessionKey = $result['session_key'] ?? '';
            $unionId = $result['unionid'] ?? '';

            // 解密用户信息（如果提供了加密数据）
            $userInfo = [];
            if (!empty($encryptedData) && !empty($iv) && !empty($sessionKey)) {
                $userInfo = $miniCrypt->decode($iv, $sessionKey, $encryptedData);
                if (!$userInfo) {
                    MyLog::error('微信小程序用户信息解密失败', [
                        'encryptedData' => $encryptedData,
                        'iv' => $iv
                    ]);
                }
            }

            // 查找或创建用户
            $user = $this->findOrCreateUser($openid, $unionId, $userInfo, 'miniapp');

            if (!$user) {
                return false;
            }

            return [
                'user_id' => $user['id'],
                'user_info' => $user,
                'openid' => $openid,
                'session_key' => $sessionKey
            ];

        } catch (\Exception $e) {
            MyLog::error('微信小程序登录异常：' . $e->getMessage(), [
                'params' => $params,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    /**
     * 微信公众号登录
     * @param array $params
     * @return array|false
     */
    public function officialLogin($params)
    {
        try {
            $code = $params['code'] ?? '';

            if (empty($code)) {
                return false;
            }

            // 获取微信公众号配置
            $config = Config::get('wechat.official');

            // 通过code获取access_token和openid
            $oauth = Oauth::instance($config);
            $result = $oauth->getOauthAccessToken($code);

            if (empty($result['openid'])) {
                MyLog::error('微信公众号登录失败：获取openid失败', $result);
                return false;
            }

            $openid = $result['openid'];
            $accessToken = $result['access_token'] ?? '';
            $unionId = $result['unionid'] ?? '';

            // 获取用户信息
            $userInfo = [];
            if (!empty($accessToken) && !empty($openid)) {
                $userInfo = $oauth->getUserInfo($accessToken, $openid);
            }

            // 查找或创建用户
            $user = $this->findOrCreateUser($openid, $unionId, $userInfo, 'official');

            if (!$user) {
                return false;
            }

            return [
                'user_id' => $user['id'],
                'user_info' => $user,
                'openid' => $openid,
                'access_token' => $accessToken
            ];

        } catch (\Exception $e) {
            MyLog::error('微信公众号登录异常：' . $e->getMessage(), [
                'params' => $params,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    /**
     * 查找或创建用户
     * @param string $openid
     * @param string $unionId
     * @param array $userInfo
     * @param string $platform
     * @return array|false
     */
    protected function findOrCreateUser($openid, $unionId = '', $userInfo = [], $platform = 'miniapp')
    {
        try {
            // 根据unionId查找用户（如果有unionId）
            $user = null;
            if (!empty($unionId)) {
                $user = $this->commonUserModel->where([
                    ['union_id', '=', $unionId],
                    ['status', '=', 1]
                ])->find();
            }
            
            // 如果没有找到，根据openid查找
            if (!$user) {
                $openidField = $platform === 'miniapp' ? 'openid_mp' : 'openid_qq';
                $user = $this->commonUserModel->where([
                    [$openidField, '=', $openid],
                    ['status', '=', 1]
                ])->find();
            }
            
            // 如果用户存在，更新信息
            if ($user) {
                $updateData = [
                    'update_time' => date('Y-m-d H:i:s')
                ];
                
                // 更新unionId
                if (!empty($unionId) && empty($user['union_id'])) {
                    $updateData['union_id'] = $unionId;
                }
                
                // 更新openid
                $openidField = $platform === 'miniapp' ? 'openid_mp' : 'openid_qq';
                if (empty($user[$openidField])) {
                    $updateData[$openidField] = $openid;
                }
                
                // 更新用户信息
                if (!empty($userInfo)) {
                    if (!empty($userInfo['nickname']) && empty($user['nickname'])) {
                        $updateData['nickname'] = $userInfo['nickname'];
                    }
                    if (!empty($userInfo['headimgurl']) && empty($user['avatar_url'])) {
                        $updateData['avatar_url'] = $userInfo['headimgurl'];
                    }
                    if (!empty($userInfo['sex'])) {
                        $updateData['gender'] = $userInfo['sex'] == 1 ? '男' : ($userInfo['sex'] == 2 ? '女' : '未知');
                    }
                    if (!empty($userInfo['country'])) {
                        $updateData['country'] = $userInfo['country'];
                    }
                    if (!empty($userInfo['province'])) {
                        $updateData['province'] = $userInfo['province'];
                    }
                    if (!empty($userInfo['city'])) {
                        $updateData['city'] = $userInfo['city'];
                    }
                }
                
                $this->commonUserModel->where('id', $user['id'])->update($updateData);
                
                // 重新获取用户信息
                $user = $this->commonUserModel->where('id', $user['id'])->find();
            } else {
                // 创建新用户
                $userData = [
                    'union_id' => $unionId,
                    'nickname' => !empty($userInfo['nickname']) ? $userInfo['nickname'] : '微信用户' . generateRandomString(6),
                    'avatar_url' => $userInfo['headimgurl'] ?? '',
                    'gender' => !empty($userInfo['sex']) ? ($userInfo['sex'] == 1 ? '男' : ($userInfo['sex'] == 2 ? '女' : '未知')) : '未知',
                    'country' => $userInfo['country'] ?? '',
                    'province' => $userInfo['province'] ?? '',
                    'city' => $userInfo['city'] ?? '',
                    'platform' => $platform,
                    'status' => 1,
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ];
                
                // 设置对应的openid
                if ($platform === 'miniapp') {
                    $userData['openid_mp'] = $openid;
                } else {
                    $userData['openid_qq'] = $openid;
                }
                
                $userId = $this->commonUserModel->insertGetId($userData);
                
                if ($userId) {
                    // 触发用户注册成功事件
                    Event::trigger(new UserRegistered($userId, $userData, 'wechat_' . $platform));
                    
                    // 获取新创建的用户信息
                    $user = $this->commonUserModel->where('id', $userId)->find();
                } else {
                    return false;
                }
            }
            
            return $user ? $user->toArray() : false;
            
        } catch (\Exception $e) {
            MyLog::error('查找或创建用户失败：' . $e->getMessage(), [
                'openid' => $openid,
                'unionId' => $unionId,
                'platform' => $platform,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    

    
    /**
     * 获取微信公众号授权URL
     * @param string $redirectUri
     * @param string $state
     * @return string
     */
    public function getOfficialAuthUrl($redirectUri, $state = '')
    {
        try {
            $config = Config::get('wechat.official');
            $oauth = Oauth::instance($config);

            return $oauth->getOauthRedirect($redirectUri, $state, 'snsapi_userinfo');
        } catch (\Exception $e) {
            MyLog::error('获取微信公众号授权URL失败：' . $e->getMessage());
            return '';
        }
    }
}
