<?php return array(
    'root' => array(
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => '2e7e75e25168c43c3a39d0fd26bcd6ffdf9f6195',
        'name' => 'topthink/think',
        'dev' => true,
    ),
    'versions' => array(
        'aliyuncs/oss-sdk-php' => array(
            'pretty_version' => 'v2.7.2',
            'version' => '2.7.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aliyuncs/oss-sdk-php',
            'aliases' => array(),
            'reference' => '483dd0b8bff5d47f0e4ffc99f6077a295c5ccbb5',
            'dev_requirement' => false,
        ),
        'guzzlehttp/command' => array(
            'pretty_version' => '1.3.2',
            'version' => '1.3.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/command',
            'aliases' => array(),
            'reference' => '888e74fc1d82a499c1fd6726248ed0bc0886395e',
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle-services' => array(
            'pretty_version' => '1.4.2',
            'version' => '1.4.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle-services',
            'aliases' => array(),
            'reference' => '45bfeb80d5ed072bb39e9f6ed1ec5d650edae961',
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'dev_requirement' => false,
        ),
        'guzzlehttp/uri-template' => array(
            'pretty_version' => 'v1.0.4',
            'version' => '1.0.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/uri-template',
            'aliases' => array(),
            'reference' => '30e286560c137526eccd4ce21b2de477ab0676d2',
            'dev_requirement' => false,
        ),
        'lcobucci/clock' => array(
            'pretty_version' => '3.3.1',
            'version' => '3.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lcobucci/clock',
            'aliases' => array(),
            'reference' => 'db3713a61addfffd615b79bf0bc22f0ccc61b86b',
            'dev_requirement' => false,
        ),
        'lcobucci/jwt' => array(
            'pretty_version' => '4.3.0',
            'version' => '4.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lcobucci/jwt',
            'aliases' => array(),
            'reference' => '4d7de2fe0d51a96418c0d04004986e410e87f6b4',
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '1.1.10',
            'version' => '1.1.10.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'reference' => '3239285c825c152bcc315fe0e87d6b55f5972ed1',
            'dev_requirement' => false,
        ),
        'league/flysystem-cached-adapter' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-cached-adapter',
            'aliases' => array(),
            'reference' => 'd1925efb2207ac4be3ad0c40b8277175f99ffaff',
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.16.0',
            'version' => '1.16.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'reference' => '2d6702ff215bf922936ccc1ad31007edc76451b9',
            'dev_requirement' => false,
        ),
        'myclabs/php-enum' => array(
            'pretty_version' => '1.8.5',
            'version' => '1.8.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/php-enum',
            'aliases' => array(),
            'reference' => 'e7be26966b7398204a234f8673fdad5ac6277802',
            'dev_requirement' => false,
        ),
        'phpmailer/phpmailer' => array(
            'pretty_version' => 'v6.10.0',
            'version' => '6.10.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmailer/phpmailer',
            'aliases' => array(),
            'reference' => 'bf74d75a1fde6beaa34a0ddae2ec5fce0f72a144',
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'reference' => 'd11b50ad223250cf17b86e38383413f5a6764bf8',
            'dev_requirement' => false,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'dev_requirement' => false,
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.1',
            'version' => '1.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'reference' => 'cb6ce4845ce34a8ad9e68117c10ee90a29919eba',
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'dev_requirement' => false,
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'dev_requirement' => false,
        ),
        'qcloud/cos-sdk-v5' => array(
            'pretty_version' => 'v2.6.16',
            'version' => '2.6.16.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../qcloud/cos-sdk-v5',
            'aliases' => array(),
            'reference' => '22366f4b4f7f277e67aa72eea8d1e02a5f9943e2',
            'dev_requirement' => false,
        ),
        'qiniu/php-sdk' => array(
            'pretty_version' => 'v7.14.0',
            'version' => '7.14.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../qiniu/php-sdk',
            'aliases' => array(),
            'reference' => 'ee752ffa7263ce99fca0bd7340cf13c486a3516c',
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'reference' => '74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'dev_requirement' => true,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'reference' => '9c46038cd4ed68952166cf7001b54eb539184ccb',
            'dev_requirement' => true,
        ),
        'thans/tp-jwt-auth' => array(
            'pretty_version' => 'v2.2.1',
            'version' => '2.2.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../thans/tp-jwt-auth',
            'aliases' => array(),
            'reference' => 'c7dd8e946a496dd0cdc920117ac0e673d9fa882f',
            'dev_requirement' => false,
        ),
        'topthink/framework' => array(
            'pretty_version' => 'v8.1.2',
            'version' => '8.1.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/framework',
            'aliases' => array(),
            'reference' => '8faec5c9b7a7f2a66ca3140a57e81bd6cd37567c',
            'dev_requirement' => false,
        ),
        'topthink/think' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => '2e7e75e25168c43c3a39d0fd26bcd6ffdf9f6195',
            'dev_requirement' => false,
        ),
        'topthink/think-container' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-container',
            'aliases' => array(),
            'reference' => 'b2df244be1e7399ad4c8be1ccc40ed57868f730a',
            'dev_requirement' => false,
        ),
        'topthink/think-dumper' => array(
            'pretty_version' => 'v1.0.5',
            'version' => '1.0.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-dumper',
            'aliases' => array(),
            'reference' => 'eba662a1843d5db68059050c530f7d43287289fc',
            'dev_requirement' => true,
        ),
        'topthink/think-filesystem' => array(
            'pretty_version' => 'v2.0.3',
            'version' => '2.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-filesystem',
            'aliases' => array(),
            'reference' => 'e8e51adb9f3a3f3aac2aa3ef73b7b439100f777d',
            'dev_requirement' => false,
        ),
        'topthink/think-helper' => array(
            'pretty_version' => 'v3.1.11',
            'version' => '3.1.11.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-helper',
            'aliases' => array(),
            'reference' => '1d6ada9b9f3130046bf6922fe1bd159c8d88a33c',
            'dev_requirement' => false,
        ),
        'topthink/think-multi-app' => array(
            'pretty_version' => 'v1.1.1',
            'version' => '1.1.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-multi-app',
            'aliases' => array(),
            'reference' => 'f93c604d5cfac2b613756273224ee2f88e457b88',
            'dev_requirement' => false,
        ),
        'topthink/think-orm' => array(
            'pretty_version' => 'v4.0.36',
            'version' => '4.0.36.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-orm',
            'aliases' => array(),
            'reference' => '412a157336d3abf34021b66ca79f235005a21c0f',
            'dev_requirement' => false,
        ),
        'topthink/think-trace' => array(
            'pretty_version' => 'v1.6',
            'version' => '1.6.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-trace',
            'aliases' => array(),
            'reference' => '136cd5d97e8bdb780e4b5c1637c588ed7ca3e142',
            'dev_requirement' => true,
        ),
        'topthink/think-validate' => array(
            'pretty_version' => 'v3.0.5',
            'version' => '3.0.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-validate',
            'aliases' => array(),
            'reference' => 'f7dd85675270e9f8c0b04a13362133067629f53c',
            'dev_requirement' => false,
        ),
        'yansongda/artful' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yansongda/artful',
            'aliases' => array(),
            'reference' => 'ddc203ef34ab369a5a31df057a0fda697d3ed855',
            'dev_requirement' => false,
        ),
        'yansongda/pay' => array(
            'pretty_version' => 'v3.7.18',
            'version' => '3.7.18.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yansongda/pay',
            'aliases' => array(),
            'reference' => '813c01e7abed94d2c5ac1a3abdfb87316d78c276',
            'dev_requirement' => false,
        ),
        'yansongda/supports' => array(
            'pretty_version' => 'v4.0.12',
            'version' => '4.0.12.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yansongda/supports',
            'aliases' => array(),
            'reference' => '308de376d20cb1cd4f959644793e0582ccd1ef6d',
            'dev_requirement' => false,
        ),
        'zoujingli/wechat-developer' => array(
            'pretty_version' => 'v1.2.75',
            'version' => '1.2.75.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../zoujingli/wechat-developer',
            'aliases' => array(),
            'reference' => '15622a40090e3e656eff517ffb4ca57227b0fa7f',
            'dev_requirement' => false,
        ),
    ),
);
