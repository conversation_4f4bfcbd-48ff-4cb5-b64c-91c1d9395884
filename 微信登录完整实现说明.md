# 微信登录完整实现说明

## 概述

基于 `zoujingli/wechat-developer` v1.2+ 实现的微信小程序登录和微信公众号登录功能，已完成与 CommonUser 系统的完整集成。

## 已修复的问题

### 1. 类名和方法名修正
- ✅ 修正了 `WeChat\MiniApp` → `WeMini\Crypt`
- ✅ 修正了 `WeChat\Official` → `WeChat\Oauth`
- ✅ 修正了配置字段 `secret` → `appsecret`
- ✅ 修正了方法名 `getOauthUserInfo` → `getUserInfo`

### 2. 正确的类使用方式
```php
// 微信小程序
$miniCrypt = Crypt::instance($config);
$result = $miniCrypt->session($code);
$userInfo = $miniCrypt->decode($iv, $sessionKey, $encryptedData);

// 微信公众号
$oauth = Oauth::instance($config);
$result = $oauth->getOauthAccessToken($code);
$userInfo = $oauth->getUserInfo($accessToken, $openid);
$authUrl = $oauth->getOauthRedirect($redirectUri, $state, 'snsapi_userinfo');
```

## 文件结构

```
tp8/
├── config/
│   └── wechat.php                          # 微信配置文件
├── app/common_api/
│   ├── controller/
│   │   └── Weixin.php                      # 微信登录控制器
│   └── service/
│       ├── WeixinService.php               # 微信登录服务类
│       └── CommonUserService.php          # 用户服务类（已扩展）
├── route/
│   └── app.php                             # 路由配置
├── .env.example                            # 环境配置示例
└── test_wechat_config.php                  # 配置测试文件
```

## 核心功能

### 1. 微信小程序登录
- 支持 `wx.login()` 获取的 code 登录
- 支持用户信息解密（encryptedData + iv）
- 自动创建/更新用户信息
- 返回 JWT Token

### 2. 微信公众号登录
- 支持网页授权登录
- 获取用户基本信息
- 支持 snsapi_userinfo 授权范围
- 返回 JWT Token

### 3. 用户数据管理
- 自动关联 UnionID（多平台账号统一）
- 同步用户信息（昵称、头像、地理位置等）
- 与现有 CommonUser 系统完美集成
- 支持账号绑定功能

## API 接口

### 微信登录接口
```
POST /api/wechat/miniapp/login          # 微信小程序登录
POST /api/wechat/official/login         # 微信公众号登录
GET  /api/wechat/official/auth-url      # 获取公众号授权URL
POST /api/wechat/miniapp/phone-login    # 小程序手机号授权登录
POST /api/wechat/bind                   # 绑定微信账号（需登录）
```

### 用户管理接口
```
POST /api/user/login                    # 密码登录
POST /api/user/register                 # 用户注册
POST /api/user/email-register           # 邮箱注册
GET  /api/user/info                     # 获取用户信息（需登录）
POST /api/user/change-password          # 修改密码（需登录）
POST /api/user/update-nickname          # 修改昵称（需登录）
POST /api/user/upload-avatar            # 上传头像（需登录）
```

## 配置步骤

### 1. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑 .env 文件，填入微信配置
WECHAT_MINIAPP_APPID=your_miniapp_appid
WECHAT_MINIAPP_SECRET=your_miniapp_secret
WECHAT_OFFICIAL_APPID=your_official_appid
WECHAT_OFFICIAL_SECRET=your_official_secret
```

### 2. 数据库配置
确保 `common_user` 表包含以下字段：
```sql
ALTER TABLE `common_user` 
ADD COLUMN `union_id` varchar(255) DEFAULT NULL COMMENT '微信UnionID',
ADD COLUMN `openid_mp` varchar(255) DEFAULT NULL COMMENT '微信小程序OpenID',
ADD COLUMN `openid_qq` varchar(255) DEFAULT NULL COMMENT '微信公众号OpenID';
```

### 3. 微信后台配置
- **小程序**: 配置服务器域名，添加 `your-domain.com`
- **公众号**: 配置网页授权域名，添加 `your-domain.com`
- **开放平台**: 绑定小程序和公众号以获取 UnionID

## 测试验证

### 1. 配置测试
```bash
cd tp8
php test_wechat_config.php
```

### 2. 接口测试
使用 Postman 或其他工具测试：

```bash
# 测试小程序登录
curl -X POST http://your-domain.com/api/wechat/miniapp/login \
  -H "Content-Type: application/json" \
  -d '{"code":"your_wechat_code"}'

# 测试获取公众号授权URL
curl http://your-domain.com/api/wechat/official/auth-url?redirect_uri=https://your-domain.com/callback
```

## 注意事项

1. **配置字段名**: 使用 `appsecret` 而不是 `secret`
2. **类实例化**: 使用 `::instance($config)` 方法
3. **错误处理**: 所有异常都会记录到日志中
4. **安全性**: 生产环境请配置 HTTPS
5. **缓存**: 建议配置 Redis 缓存以提高性能

## 常见问题

### Q: 提示类不存在？
A: 确保 `zoujingli/wechat-developer` 包已正确安装，运行 `composer install`

### Q: 获取不到 openid？
A: 检查微信配置是否正确，code 是否有效（只能使用一次）

### Q: 用户信息解密失败？
A: 确保 session_key、encryptedData、iv 参数完整且正确

### Q: 公众号授权失败？
A: 检查域名是否在微信公众号后台正确配置

## 扩展功能

可以基于现有代码继续扩展：
- 微信支付集成
- 小程序订阅消息
- 公众号模板消息
- 微信客服消息
- 用户标签管理

## 技术支持

如遇到问题，请检查：
1. 日志文件：`runtime/log/`
2. 微信开发者工具控制台
3. 服务器错误日志
4. 网络连接状态
