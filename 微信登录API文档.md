# 微信登录API文档

## 概述

本文档描述了基于 `zoujingli/wechat-developer` 包实现的微信小程序登录和微信公众号登录功能。

## 配置说明

### 1. 环境配置

复制 `.env.example` 文件为 `.env`，并配置以下微信相关参数：

```env
# 微信小程序配置
WECHAT_MINIAPP_APPID = your_miniapp_appid
WECHAT_MINIAPP_SECRET = your_miniapp_secret
WECHAT_MINIAPP_TOKEN = your_miniapp_token
WECHAT_MINIAPP_AES_KEY = your_miniapp_aes_key

# 微信公众号配置
WECHAT_OFFICIAL_APPID = your_official_appid
WECHAT_OFFICIAL_SECRET = your_official_secret
WECHAT_OFFICIAL_TOKEN = your_official_token
WECHAT_OFFICIAL_AES_KEY = your_official_aes_key
```

### 2. 数据库表结构

微信登录功能使用 `common_user` 表，主要字段说明：

- `union_id`: 微信开放平台统一ID
- `openid_mp`: 微信小程序openid
- `openid_qq`: 微信公众号openid
- `nickname`: 用户昵称
- `avatar_url`: 用户头像URL
- `gender`: 性别
- `country`: 国家
- `province`: 省份
- `city`: 城市

## API接口

### 1. 微信小程序登录

**接口地址：** `POST /api/wechat/miniapp/login`

**请求参数：**
```json
{
    "code": "微信小程序wx.login()获取的code",
    "encryptedData": "加密的用户信息（可选）",
    "iv": "初始向量（可选）"
}
```

**响应示例：**
```json
{
    "error_code": 0,
    "msg": "登录成功",
    "data": {
        "token": "jwt_token_string",
        "user_info": {
            "id": 1,
            "nickname": "用户昵称",
            "avatar_url": "头像URL",
            "gender": "男",
            "country": "中国",
            "province": "广东省",
            "city": "深圳市"
        },
        "openid": "微信openid"
    }
}
```

### 2. 微信公众号登录

**接口地址：** `POST /api/wechat/official/login`

**请求参数：**
```json
{
    "code": "微信公众号授权后获取的code"
}
```

**响应示例：**
```json
{
    "error_code": 0,
    "msg": "登录成功",
    "data": {
        "token": "jwt_token_string",
        "user_info": {
            "id": 1,
            "nickname": "用户昵称",
            "avatar_url": "头像URL",
            "gender": "男"
        },
        "openid": "微信openid"
    }
}
```

### 3. 获取微信公众号授权URL

**接口地址：** `GET /api/wechat/official/auth-url`

**请求参数：**
```
redirect_uri: 授权后回调地址
state: 状态参数（可选）
```

**响应示例：**
```json
{
    "error_code": 0,
    "msg": "获取授权URL成功",
    "data": {
        "auth_url": "https://open.weixin.qq.com/connect/oauth2/authorize?..."
    }
}
```

### 4. 微信小程序手机号授权登录

**接口地址：** `POST /api/wechat/miniapp/phone-login`

**请求参数：**
```json
{
    "code": "微信小程序wx.login()获取的code",
    "phone_code": "手机号授权获取的code"
}
```

### 5. 绑定微信账号到现有用户

**接口地址：** `POST /api/wechat/bind`

**请求头：**
```
Authorization: Bearer jwt_token
```

**请求参数：**
```json
{
    "code": "微信授权code",
    "platform": "miniapp|official"
}
```

## 使用流程

### 微信小程序登录流程

1. 小程序端调用 `wx.login()` 获取 code
2. （可选）调用 `wx.getUserProfile()` 获取用户信息
3. 将 code 和用户信息发送到后端 `/api/wechat/miniapp/login`
4. 后端返回 JWT token 和用户信息
5. 前端保存 token 用于后续API调用

### 微信公众号登录流程

1. 后端调用 `/api/wechat/official/auth-url` 获取授权URL
2. 前端跳转到授权URL进行授权
3. 用户授权后微信会回调到指定地址，携带 code 参数
4. 前端获取 code 后调用 `/api/wechat/official/login`
5. 后端返回 JWT token 和用户信息

## 注意事项

1. 所有需要登录的接口都需要在请求头中携带 `Authorization: Bearer jwt_token`
2. 微信登录会自动创建用户账号，如果用户已存在则更新用户信息
3. 支持通过 unionId 关联同一用户在不同平台的账号
4. 建议在生产环境中配置微信开放平台以获取 unionId
5. 用户信息会根据微信返回的数据自动更新，包括头像、昵称、地理位置等

## 错误码说明

- `0`: 成功
- `1`: 参数错误
- `20001`: token验证失败
- 其他错误码请参考具体接口返回信息
